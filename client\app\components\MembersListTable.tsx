export type MembersListTableProps = {
    members: { id: string, name: string, avatarUrl: string, role: string }[]
}

export default function MembersListTable({ members }: MembersListTableProps) {
    if (!members || members.length === 0) {
        return (
            <div className="text-center py-8 text-base-content/60">
                <span className="material-symbols-outlined text-2xl mb-2 block">person_off</span>
                <p>No members in this role</p>
            </div>
        );
    }

    return (
        <div className="space-y-3 member-scroll max-h-96 overflow-y-auto">
            {members.map((member, index) => (
                <div
                    key={member.id || index}
                    className="flex items-center gap-4 p-4 bg-base-50 hover:bg-base-200 rounded-lg member-card"
                >
                    <div className="avatar">
                        <div className="w-12 h-12 rounded-full ring-2 ring-base-300">
                            <img
                                src={member.avatarUrl || "https://upload.wikimedia.org/wikipedia/commons/7/7c/Profile_avatar_placeholder_large.png"}
                                alt={`${member.name}'s avatar`}
                                className="object-cover"
                            />
                        </div>
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-base-content truncate">
                            {member.name}
                        </h4>
                        <p className="text-sm text-base-content/60 truncate">
                            {member.role}
                        </p>
                    </div>

                    <div className="flex-shrink-0">
                        <div className="dropdown dropdown-end">
                            <div tabIndex={0} role="button" className="btn btn-ghost btn-sm btn-circle">
                                <span className="material-symbols-outlined text-base-content/60">more_vert</span>
                            </div>
                            <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-300">
                                <li>
                                    <a className="text-sm">
                                        <span className="material-symbols-outlined text-info">visibility</span>
                                        View Profile
                                    </a>
                                </li>
                                <li>
                                    <a className="text-sm">
                                        <span className="material-symbols-outlined text-warning">edit</span>
                                        Edit Role
                                    </a>
                                </li>
                                <li>
                                    <a className="text-sm text-error">
                                        <span className="material-symbols-outlined">person_remove</span>
                                        Remove Member
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}

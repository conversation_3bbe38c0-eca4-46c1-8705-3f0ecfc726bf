@import "tailwindcss";

@plugin "daisyui" {
  themes: light --default, dark --prefersdark;
}

/* Custom animations and polish for Company Details */
.company-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.company-card:hover {
  transform: translateY(-2px);
}

.member-card {
  transition: all 0.2s ease-in-out;
}

.member-card:hover {
  transform: translateX(4px);
}

/* Smooth fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Enhanced focus states */
.btn:focus-visible,
.input:focus,
.textarea:focus,
.file-input:focus {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Custom scrollbar for member lists */
.member-scroll::-webkit-scrollbar {
  width: 6px;
}

.member-scroll::-webkit-scrollbar-track {
  background: hsl(var(--b2));
  border-radius: 3px;
}

.member-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.3);
  border-radius: 3px;
}

.member-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.5);
}
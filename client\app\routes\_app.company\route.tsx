import { LoaderFunctionArgs, ActionFunctionArgs, redirect, HeadersFunction } from "@remix-run/node";
import RouteLayout from "~/layouts/RouteLayout";
import { Form, Link, useLoaderData, useOutletContext, useNavigation, useActionData } from "@remix-run/react";
import { useState, useRef, useEffect } from "react";
import { EditModeForm } from "~/components/EditModeForm";
import apiClient from "~/services/api.server/apiClient";
import { AuthenticationError } from "~/services/api.server/errors";
import { CompanyInfoResponse, UserInfoResponse } from "~/services/api.server/types";
import { getSession } from "~/services/sessions.server";
import { useEditMode } from "~/utils/editMode";
import { ActionResponse, ActionResponseResult, ForbiddenResponse, JsonResponse } from "~/utils/response";
import tryCatch from "~/utils/tryCatch";
import { validateRole } from "~/utils/validate";
import permissions from "~/data/permissions";
import roleNames from "~/data/roles";
import MembersListTable from "~/components/MembersListTable";

export const handle = {
    breadcrumb: () => <Link to="/company">Company</Link>,
};

// TODO: Handle when user gets kicked from company
// TODO: Handle role permissons on this page (edit company info, manage invites)

export async function loader({ request }: LoaderFunctionArgs) {
    const session = await getSession(request);

    const {
        data: tokenResponse,
        error: tokenError
    } = await tryCatch(apiClient.auth.getValidToken(session));

    if (tokenError) {
        return redirect("/logout");
    }

    const {
        data: res,
        error
    } = await tryCatch(apiClient.getCompanyInfo(tokenResponse.token));

    if (error instanceof AuthenticationError) {
        return redirect("/logout");
    }
    if (error) {
        return JsonResponse({
            data: null,
            error: error.message,
            headers: tokenResponse.headers
        });
    }

    return JsonResponse({
        data: res,
        error: null,
        headers: tokenResponse.headers
    });

}

export async function action({ request }: ActionFunctionArgs) {
    const session = await getSession(request);
    const userRole = session.get("user").role;

    if (!validateRole(userRole, permissions.company.edit)) {
        return ForbiddenResponse()
    }

    const { data: tokenResponse, error: tokenError } = await tryCatch(apiClient.auth.getValidToken(session));
    if (tokenError) {
        return redirect("/logout");
    }

    const formData = await request.formData();
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const websiteUrl = formData.get("websiteUrl") as string || null;
    const removeLogo = formData.get("removeLogo") === "true";

    // Create a FormData object for the multipart request
    const requestFormData = new FormData();
    requestFormData.append("name", name);
    requestFormData.append("description", description);
    requestFormData.append("websiteUrl", websiteUrl ?? "");
    requestFormData.append("removeLogo", removeLogo.toString());

    // Handle file upload if provided
    const logoFile = formData.get("logo") as File;

    const maxMB = 2;
    if (logoFile.size > maxMB * 1024 * 1024) {
        return ActionResponse({ success: false, error: `Logo file is too large. Maximum size is ${maxMB}MB.`, headers: tokenResponse.headers })
    }
    if (logoFile && logoFile.size > 0) {
        requestFormData.append("file", logoFile);
    }

    const { error } = await tryCatch(apiClient.updateCompanyInfo(requestFormData, tokenResponse.token));

    if (error instanceof AuthenticationError) {
        return redirect("/logout");
    }

    if (error) {
        return ActionResponse({ success: false, error: error.message, headers: tokenResponse.headers })
    }

    return ActionResponse({ success: true, error: null, headers: tokenResponse.headers })
}

export default function CompanyRoute() {
    const { data, error } = useLoaderData<typeof loader>();
    const actionData = useActionData<typeof action>() as ActionResponseResult;
    const userData = useOutletContext<UserInfoResponse>();
    const navigation = useNavigation();
    const isSubmitting = navigation.state === "submitting";
    const canEdit = validateRole(userData.role, permissions.company.edit);

    // State for edit mode
    const {
        isEditing,
        formError,
        toggleEditMode,
    } = useEditMode({ actionData });



    const [removeLogo, setRemoveLogo] = useState(false);
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const companyInfo = data as CompanyInfoResponse | null;
    console.log(companyInfo?.members)
    const memberList = Object.entries(companyInfo?.members ?? {}).map(([role, members]) => {
        return (
            <div key={role} className="my-4 bg-base-300 p-4 rounded-sm">
                <h3 className="text-lg font-medium">{role}</h3>
                <MembersListTable members={members as any} />
            </div>
        );
    })


    // Handle file selection for preview
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result as string);
                setRemoveLogo(false);
            };
            reader.readAsDataURL(file);
        } else {
            setPreviewImage(null);
        }
    };

    // Handle remove logo checkbox
    const handleRemoveLogoChange = () => {
        setRemoveLogo(!removeLogo);
        if (!removeLogo) {
            setPreviewImage(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    // Reset preview when exiting edit mode
    useEffect(() => {
        if (!isEditing) {
            setPreviewImage(null);
        }
    }, [isEditing]);

    return (
        <RouteLayout className="max-w-7xl mx-auto">
            {error && (
                <div className="alert alert-error mb-6">
                    <span className="material-symbols-outlined">error</span>
                    <span>{error}</span>
                </div>
            )}

            {companyInfo && (
                <div className="space-y-6 fade-in-up">
                    {/* Company Header Section */}
                    <div className="card bg-base-100 shadow-lg company-card">
                        <div className="card-body p-8 relative">
                            {/* Action Buttons */}
                            {canEdit && !isEditing && (
                                <div className="absolute top-6 right-6 flex gap-2 z-10">
                                    <button
                                        onClick={() => { toggleEditMode(); }}
                                        className="btn btn-ghost btn-sm hover:bg-primary/10 transition-colors"
                                    >
                                        <span className="material-symbols-outlined text-primary">edit</span>
                                        <span className="hidden sm:inline">Edit Company</span>
                                    </button>
                                    <Link
                                        to="/company/invites"
                                        className="btn btn-ghost btn-sm hover:bg-accent/10 transition-colors"
                                    >
                                        <span className="material-symbols-outlined text-accent">settings</span>
                                        <span className="hidden sm:inline">Manage Roles</span>
                                    </Link>
                                </div>
                            )}

                            {isEditing ? (
                                <EditModeForm
                                    error={formError}
                                    isSubmitting={isSubmitting}
                                    encType="multipart/form-data"
                                    onCancel={() => { toggleEditMode(); }}>

                                    {/* Logo Upload Section */}
                                    <div className="flex flex-col sm:flex-row gap-6 mb-8">
                                        <div className="flex-shrink-0">
                                            <div className="avatar">
                                                <div className="w-32 h-32 rounded-xl bg-base-200 border-2 border-dashed border-base-300 grid place-items-center overflow-hidden hover:border-primary/50 transition-colors">
                                                    {!removeLogo && (
                                                        <img
                                                            src={previewImage ||
                                                                companyInfo.logoUrl ||
                                                                "https://upload.wikimedia.org/wikipedia/commons/7/7c/Profile_avatar_placeholder_large.png"}
                                                            alt="Company logo"
                                                            className="object-cover w-full h-full rounded-xl"
                                                        />
                                                    )}
                                                    {removeLogo && (
                                                        <div className="text-center">
                                                            <span className="material-symbols-outlined text-base-content/30 text-4xl">image_not_supported</span>
                                                            <p className="text-xs text-base-content/50 mt-1">No logo</p>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex-1 space-y-4">
                                            <div className="form-control">
                                                <label className="label">
                                                    <span className="label-text font-medium">Company Logo</span>
                                                </label>
                                                <input
                                                    type="file"
                                                    name="logo"
                                                    ref={fileInputRef}
                                                    className="file-input file-input-bordered w-full"
                                                    accept="image/jpg, image/jpeg, image/png, image/svg+xml, image/webp"
                                                    onChange={handleFileChange}
                                                    disabled={removeLogo}
                                                />
                                                <div className="label">
                                                    <span className="label-text-alt">Recommended: 512x512px, max 5MB</span>
                                                </div>
                                            </div>

                                            <div className="form-control">
                                                <label className="label cursor-pointer justify-start gap-3">
                                                    <input
                                                        type="checkbox"
                                                        className="checkbox checkbox-sm"
                                                        checked={removeLogo}
                                                        onChange={handleRemoveLogoChange}
                                                    />
                                                    <span className="label-text">Remove current logo</span>
                                                </label>
                                                <input type="hidden" name="removeLogo" value={removeLogo.toString()} />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Company Details Form */}
                                    <div className="grid grid-cols-1 gap-6">
                                        <div className="form-control">
                                            <label className="label">
                                                <span className="label-text font-medium">Company Name</span>
                                            </label>
                                            <input
                                                type="text"
                                                name="name"
                                                className="input input-bordered w-full"
                                                defaultValue={companyInfo.name}
                                                required
                                                maxLength={75}
                                                placeholder="Enter company name"
                                            />
                                        </div>

                                        <div className="form-control">
                                            <label className="label">
                                                <span className="label-text font-medium">Description</span>
                                            </label>
                                            <textarea
                                                name="description"
                                                className="textarea textarea-bordered w-full resize-none"
                                                defaultValue={companyInfo.description}
                                                rows={4}
                                                required
                                                maxLength={1000}
                                                placeholder="Describe your company..."
                                            ></textarea>
                                            <div className="label">
                                                <span className="label-text-alt">Maximum 1000 characters</span>
                                            </div>
                                        </div>
                                    </div>
                                </EditModeForm>
                            ) : (
                                <>
                                    {/* Company Display */}
                                    <div className="flex flex-col sm:flex-row gap-6 items-start">
                                        <div className="flex-shrink-0">
                                            <div className="avatar">
                                                <div className="w-24 sm:w-32 rounded-xl shadow-lg">
                                                    <img
                                                        src={companyInfo.logoUrl ?? "https://upload.wikimedia.org/wikipedia/commons/7/7c/Profile_avatar_placeholder_large.png"}
                                                        alt={`${companyInfo.name} logo`}
                                                        className="object-cover"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex-1 min-w-0">
                                            <h1 className="text-3xl sm:text-4xl font-bold text-base-content mb-3">
                                                {companyInfo.name}
                                            </h1>
                                            <p className="text-base sm:text-lg text-base-content/80 leading-relaxed mb-4">
                                                {companyInfo.description}
                                            </p>

                                            {/* Company Stats */}
                                            <div className="flex flex-wrap gap-4 text-sm">
                                                <div className="flex items-center gap-2 text-base-content/60">
                                                    <span className="material-symbols-outlined text-lg">group</span>
                                                    <span>
                                                        {Object.values(companyInfo.members ?? {}).reduce((total, members) => total + members.length, 0)} members
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2 text-base-content/60">
                                                    <span className="material-symbols-outlined text-lg">domain</span>
                                                    <span>Company ID: {companyInfo.id?.slice(-8) || 'N/A'}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Members Section */}
                    {!isEditing && (
                        <div className="card bg-base-100 shadow-lg company-card">
                            <div className="card-body p-8">
                                <div className="flex items-center justify-between mb-6">
                                    <h2 className="text-2xl font-bold text-base-content">Team Members</h2>
                                    <div className="badge badge-neutral">
                                        {Object.values(companyInfo.members ?? {}).reduce((total, members) => total + members.length, 0)} total
                                    </div>
                                </div>

                                <div className="space-y-6">
                                    {Object.entries(companyInfo.members ?? {}).map(([role, members]) => (
                                        <div key={role} className="border border-base-300 rounded-xl overflow-hidden">
                                            <div className="bg-base-200 px-6 py-4 border-b border-base-300">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="text-lg font-semibold text-base-content capitalize">
                                                        {role}
                                                    </h3>
                                                    <div className="badge badge-primary badge-sm">
                                                        {members.length}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="p-6">
                                                <MembersListTable members={members as any} />
                                            </div>
                                        </div>
                                    ))}

                                    {Object.keys(companyInfo.members ?? {}).length === 0 && (
                                        <div className="text-center py-12 text-base-content/60">
                                            <span className="material-symbols-outlined text-4xl mb-4 block">group_off</span>
                                            <p className="text-lg">No team members found</p>
                                            <p className="text-sm">Invite members to get started</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Invites Section */}
                    <div className="card bg-base-100 shadow-lg company-card">
                        <div className="card-body p-8">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-2xl font-bold text-base-content">Invitations</h2>
                                <Link
                                    to="/company/invites"
                                    className="btn btn-primary btn-sm"
                                >
                                    <span className="material-symbols-outlined">person_add</span>
                                    Manage Invites
                                </Link>
                            </div>

                            <div className="text-center py-8 text-base-content/60">
                                <span className="material-symbols-outlined text-4xl mb-4 block">mail</span>
                                <p className="text-lg">Invitation management</p>
                                <p className="text-sm">Send and manage team invitations</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </RouteLayout>
    );
}
